# React Monorepo

A monorepo containing React TypeScript applications using Yarn workspaces.

## Structure

```
├── apps/
│   ├── admin/          # Admin React app (port 3001)
│   └── order/          # Order React app (port 3002)
├── packages/           # Shared packages (future use)
└── package.json        # Root workspace configuration
```

## Prerequisites

- Node.js >= 18.0.0
- Yarn >= 4.0.0
- Corepack enabled (`corepack enable`)

## Getting Started

1. Install dependencies:
   ```bash
   yarn install
   ```

2. Start all applications in development mode:
   ```bash
   yarn dev
   ```

3. Or start individual applications:
   ```bash
   # Admin app (http://localhost:3001)
   yarn admin:dev
   
   # Order app (http://localhost:3002)
   yarn order:dev
   ```

## Available Scripts

### Root Level Scripts

- `yarn dev` - Start all apps in development mode
- `yarn build` - Build all apps for production
- `yarn lint` - Run ESLint on all workspaces
- `yarn type-check` - Run TypeScript type checking on all workspaces
- `yarn clean` - Clean build artifacts from all workspaces

### Individual App Scripts

- `yarn admin:dev` - Start admin app in development mode
- `yarn admin:build` - Build admin app for production
- `yarn order:dev` - Start order app in development mode
- `yarn order:build` - Build order app for production

## Applications

### Admin App
- **Port**: 3001
- **Location**: `apps/admin`
- **Purpose**: Administrative interface

### Order App
- **Port**: 3002
- **Location**: `apps/order`
- **Purpose**: Order management interface

## Development

Each application is a standalone React TypeScript project created with Vite. They share common configuration and dependencies through the monorepo structure.

### Adding New Packages

To add a dependency to a specific app:
```bash
yarn workspace @monorepo/admin add <package-name>
yarn workspace @monorepo/order add <package-name>
```

To add a shared dependency at the root level:
```bash
yarn add <package-name> -W
```

### Creating Shared Packages

Shared packages should be placed in the `packages/` directory and follow the naming convention `@monorepo/<package-name>`.
