{"name": "@monorepo/admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 3001", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}