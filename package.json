{"name": "react-monorepo", "version": "1.0.0", "private": true, "description": "Monorepo for React applications (admin and order)", "workspaces": ["apps/*", "packages/*"], "packageManager": "yarn@4.0.0", "scripts": {"dev": "yarn workspaces foreach -A -p -i run dev", "build": "yarn workspaces foreach -A -p run build", "lint": "yarn workspaces foreach -A -p run lint", "type-check": "yarn workspaces foreach -A -p run type-check", "clean": "yarn workspaces foreach -A -p run clean", "admin:dev": "yarn workspace @monorepo/admin dev", "admin:build": "yarn workspace @monorepo/admin build", "order:dev": "yarn workspace @monorepo/order dev", "order:build": "yarn workspace @monorepo/order build"}, "devDependencies": {"@eslint/js": "^9.32.0", "@types/node": "^20.10.0", "eslint": "^9.32.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "^5.3.0", "typescript-eslint": "^8.38.0"}, "engines": {"node": ">=18.0.0", "yarn": ">=4.0.0"}}